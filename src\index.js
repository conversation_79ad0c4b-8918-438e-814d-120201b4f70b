import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import ejsLayouts from 'express-ejs-layouts';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import routes
import authRoutes from './routes/auth.js';
import profileRoutes from './routes/profile.js';
import resourcesRoutes from './routes/resources.js';
import uploadsRoutes from './routes/uploads.js';
import coursesRoutes from './routes/courses.js';
import paymentsRoutes from './routes/payments.js';
import adminRoutes from './routes/admin.js';
import makeAdminRoutes from './routes/makeAdmin.js';
import weatherRoutes from './routes/weatherRoutes.js';
import networkRoutes from './routes/network.js';
import messagingRoutes from './routes/messaging.js';
import transportRoutes from './routes/transport.js';

import marketTrendsRoutes from './routes/marketTrends.js';

// Import middleware
import { isAuthenticated, attachCurrentUser } from './middleware/auth.js';
import { attachAdminStatus } from './middleware/admin.js';

// Import Firebase configuration and authentication service
import { initializeFirebase } from './config/initFirebase.js';
import { getCurrentUser } from './services/firebaseService.js';

// Initialize Firebase
initializeFirebase().then(status => {
  console.log('Firebase initialization status:', status);
}).catch(error => {
  console.error('Error initializing Firebase:', error);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));
app.use(attachCurrentUser);
app.use(attachAdminStatus);

// Set up EJS and layouts
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(ejsLayouts);
app.set('layout', 'layout');
app.set("layout extractScripts", true);

// Public routes - accessible without authentication
app.use('/auth', authRoutes);

// Protected routes - require authentication
app.use('/network', isAuthenticated, networkRoutes);
app.use('/messaging', isAuthenticated, messagingRoutes);
app.use('/transport', isAuthenticated, transportRoutes);
app.use('/market-trends', isAuthenticated, marketTrendsRoutes);

// Protected routes - require authentication
app.use('/profile', isAuthenticated, profileRoutes);
app.use('/resources', isAuthenticated, resourcesRoutes);
app.use('/courses', isAuthenticated, coursesRoutes);
app.use('/payments', isAuthenticated, paymentsRoutes);
app.use('/uploads', isAuthenticated, uploadsRoutes);
app.use('/weather', isAuthenticated, weatherRoutes);



// Admin routes - require authentication and admin privileges
app.use('/admin', isAuthenticated, adminRoutes);
app.use('/make-admin', isAuthenticated, makeAdminRoutes); // Development route only



// Redirect /uploads root to /dashboard for simplicity
// Commented out to avoid conflicts with uploads API routes
// app.get('/uploads', isAuthenticated, (req, res) => {
//   res.redirect('/dashboard');
// });





// PWA Offline page
app.get('/offline.html', (req, res) => {
  res.render('offline', {
    title: 'Offline - Sustainable Farming'
  });
});

// Create sample data route (for testing)
app.get('/create-sample-data', async (req, res) => {
  try {
    const { createSampleData } = await import('./utils/createSampleData.js');
    const result = await createSampleData();
    res.json(result);
  } catch (error) {
    console.error('Error creating sample data:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test user creation (for testing)
app.get('/create-test-user', async (req, res) => {
  try {
    const { registerUser } = await import('./services/firebaseService.js');

    const testUser = {
      email: '<EMAIL>',
      password: 'test123456',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User',
      location: 'Test Location',
      farmName: 'Test Farm'
    };

    const user = await registerUser(testUser);
    res.json({
      success: true,
      message: 'Test user created successfully',
      user: {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName
      }
    });
  } catch (error) {
    console.error('Error creating test user:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Make current user admin (for testing)
app.get('/make-me-admin', async (req, res) => {
  try {
    if (!req.user) {
      return res.json({
        success: false,
        message: 'You must be logged in to become an admin'
      });
    }

    const { doc, setDoc } = await import('firebase/firestore');
    const { db } = await import('./config/firebase.js');

    // Update user document to make them admin
    const userRef = doc(db, 'users', req.user.uid);
    await setDoc(userRef, {
      uid: req.user.uid,
      email: req.user.email,
      displayName: req.user.displayName || 'Admin User',
      firstName: req.user.displayName?.split(' ')[0] || 'Admin',
      lastName: req.user.displayName?.split(' ')[1] || 'User',
      isAdmin: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }, { merge: true });

    res.json({
      success: true,
      message: 'You are now an admin! You can access /admin',
      user: {
        uid: req.user.uid,
        email: req.user.email,
        isAdmin: true
      }
    });
  } catch (error) {
    console.error('Error making user admin:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Debug admin status (for testing)
app.get('/debug-admin', async (req, res) => {
  try {
    const debugInfo = {
      isAuthenticated: !!req.user,
      user: req.user ? {
        uid: req.user.uid,
        email: req.user.email,
        displayName: req.user.displayName
      } : null,
      isAdmin: req.isAdmin,
      adminFromLocals: res.locals.isAdmin
    };

    if (req.user) {
      // Check Firestore for admin status
      const { doc, getDoc } = await import('firebase/firestore');
      const { db } = await import('./config/firebase.js');

      const userRef = doc(db, 'users', req.user.uid);
      const userDoc = await getDoc(userRef);

      debugInfo.firestoreUser = userDoc.exists() ? userDoc.data() : null;
      debugInfo.firestoreAdmin = userDoc.exists() ? userDoc.data().isAdmin : false;
    }

    res.json({
      success: true,
      debug: debugInfo,
      message: 'Admin debug information'
    });
  } catch (error) {
    console.error('Error debugging admin status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Migration endpoint to fix existing uploads
app.post('/migrate-uploads', async (req, res) => {
  try {
    console.log('Starting upload migration...');

    const { collection, getDocs, updateDoc, doc } = await import('firebase/firestore');
    const { db } = await import('./config/firebase.js');

    const uploadsRef = collection(db, 'uploads');
    const snapshot = await getDocs(uploadsRef);

    let updatedCount = 0;
    const batch = [];

    snapshot.forEach((docSnapshot) => {
      const data = docSnapshot.data();
      const needsUpdate = !data.hasOwnProperty('likes') ||
                         !data.hasOwnProperty('likeCount') ||
                         !data.hasOwnProperty('comments') ||
                         !data.hasOwnProperty('commentCount');

      if (needsUpdate) {
        batch.push({
          id: docSnapshot.id,
          updates: {
            likes: data.likes || [],
            likeCount: data.likeCount || 0,
            comments: data.comments || [],
            commentCount: data.commentCount || 0
          }
        });
      }
    });

    // Update documents
    for (const item of batch) {
      try {
        const docRef = doc(db, 'uploads', item.id);
        await updateDoc(docRef, item.updates);
        updatedCount++;
        console.log(`Updated upload ${item.id}`);
      } catch (error) {
        console.error(`Error updating upload ${item.id}:`, error);
      }
    }

    console.log(`Migration completed. Updated ${updatedCount} uploads.`);

    res.json({
      success: true,
      message: `Migration completed. Updated ${updatedCount} uploads.`,
      updatedCount
    });

  } catch (error) {
    console.error('Error during migration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Debug premium course creation (for testing)
app.post('/debug-create-premium-course', async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'You must be logged in to create a course'
      });
    }

    const { createCourse } = await import('./services/adminCourseService.js');

    const testCourseData = {
      title: 'Debug Premium Course - ' + new Date().toLocaleTimeString(),
      description: 'This is a test premium course created for debugging purposes.',
      category: 'organic-farming',
      level: 'intermediate',
      duration: 120,
      objectives: ['Learn premium farming techniques', 'Master advanced organic methods', 'Get certified in sustainable practices'],
      prerequisites: ['Basic farming knowledge', 'Access to farming equipment'],
      tags: ['premium', 'test', 'organic', 'advanced'],
      isPremium: true,
      price: 99.99,
      currency: 'USD',
      enrollmentLimit: 25,
      requiresApproval: true,
      certificateTemplate: 'professional',
      isPublished: true
    };

    console.log('Creating debug premium course with data:', testCourseData);

    const course = await createCourse(testCourseData);

    console.log('Debug premium course created:', course);

    res.json({
      success: true,
      message: 'Debug premium course created successfully',
      course: course
    });
  } catch (error) {
    console.error('Error creating debug premium course:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint for paginated content
app.get('/api/content', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filter = req.query.filter || 'all';

    console.log('API content request:', { page, limit, filter });

    // Import the firebase service
    const { getUploads } = await import('./services/firebaseService.js');

    // Get paginated uploads
    const result = await getUploads({
      page,
      limit,
      filter
    });

    console.log('API content response:', {
      uploadsCount: result.uploads ? result.uploads.length : 0,
      total: result.total,
      hasMore: result.hasMore
    });

    res.json({
      success: true,
      uploads: result.uploads || [],
      pagination: {
        page: result.page || page,
        limit: result.limit || limit,
        total: result.total || 0,
        hasMore: result.hasMore || false
      }
    });
  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      uploads: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        hasMore: false
      }
    });
  }
});

// Test endpoint to check uploads
app.get('/test/uploads', async (req, res) => {
  try {
    const { getUploads } = await import('./services/firebaseService.js');
    const result = await getUploads({ limit: 5 });
    res.json({
      success: true,
      uploads: result.uploads || [],
      total: result.total || 0
    });
  } catch (error) {
    console.error('Error getting test uploads:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to create a sample upload
app.post('/test/create-upload', async (req, res) => {
  try {
    const { addUpload } = await import('./services/firebaseService.js');

    const testUpload = {
      title: 'Test Farming Post',
      description: 'This is a test post to check like and comment functionality.',
      category: 'farming-techniques',
      fileUrl: null
    };

    // Create a test user
    const testUser = {
      uid: 'test-user',
      displayName: 'Test User',
      photoURL: null
    };

    const result = await addUpload(testUpload, testUser);
    res.json({ success: true, upload: result });
  } catch (error) {
    console.error('Error creating test upload:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to test like functionality
app.post('/test/like/:id', async (req, res) => {
  try {
    console.log('=== TEST LIKE ENDPOINT ===');
    console.log('Testing like for upload ID:', req.params.id);

    const { likeUpload, getUploadById } = await import('./services/firebaseService.js');

    // Create a test user
    const testUser = {
      uid: 'test-user',
      displayName: 'Test User',
      photoURL: null
    };

    // Get upload before like
    const uploadBefore = await getUploadById(req.params.id);
    console.log('Upload before like:', uploadBefore ? 'Found' : 'Not found');

    if (!uploadBefore) {
      return res.status(404).json({ success: false, error: 'Upload not found' });
    }

    // Perform like operation
    const result = await likeUpload(req.params.id, testUser);
    console.log('Like operation result:', result ? 'Success' : 'Failed');

    // Get upload after like
    const uploadAfter = await getUploadById(req.params.id);

    res.json({
      success: true,
      before: {
        likeCount: uploadBefore.likeCount || 0,
        likes: uploadBefore.likes || []
      },
      after: {
        likeCount: uploadAfter.likeCount || 0,
        likes: uploadAfter.likes || []
      },
      liked: (uploadAfter.likes || []).includes(testUser.uid)
    });
  } catch (error) {
    console.error('Error testing like:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test endpoint to test comment functionality
app.post('/test/comment/:id', async (req, res) => {
  try {
    console.log('=== TEST COMMENT ENDPOINT ===');
    console.log('Testing comment for upload ID:', req.params.id);

    const { addComment, getUploadById } = await import('./services/firebaseService.js');

    // Create a test user
    const testUser = {
      uid: 'test-user',
      displayName: 'Test User',
      photoURL: null
    };

    const commentText = req.body.text || 'This is a test comment';

    // Get upload before comment
    const uploadBefore = await getUploadById(req.params.id);
    console.log('Upload before comment:', uploadBefore ? 'Found' : 'Not found');

    if (!uploadBefore) {
      return res.status(404).json({ success: false, error: 'Upload not found' });
    }

    // Perform comment operation
    const result = await addComment(req.params.id, commentText, testUser);
    console.log('Comment operation result:', result ? 'Success' : 'Failed');

    // Get upload after comment
    const uploadAfter = await getUploadById(req.params.id);

    res.json({
      success: true,
      before: {
        commentCount: uploadBefore.commentCount || 0,
        comments: uploadBefore.comments || []
      },
      after: {
        commentCount: uploadAfter.commentCount || 0,
        comments: uploadAfter.comments || []
      },
      newComment: (uploadAfter.comments || []).slice(-1)[0]
    });
  } catch (error) {
    console.error('Error testing comment:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test page to display and test functionality
app.get('/test', async (req, res) => {
  try {
    const { getUploads } = await import('./services/firebaseService.js');
    const result = await getUploads({ limit: 3 });
    const uploads = result.uploads || [];

    const testPageHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>Like & Comment Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload { border: 1px solid #ccc; margin: 20px 0; padding: 15px; }
        .test-buttons { margin: 10px 0; }
        .test-buttons button { margin: 5px; padding: 5px 10px; }
        .results { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        pre { background: #eee; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Like & Comment Functionality Test</h1>

    ${uploads.map(upload => `
        <div class="upload">
            <h3>${upload.title || 'Untitled'}</h3>
            <p>${upload.description || 'No description'}</p>
            <p><strong>ID:</strong> ${upload.id}</p>
            <p><strong>Current Likes:</strong> ${upload.likeCount || 0}</p>
            <p><strong>Current Comments:</strong> ${upload.commentCount || 0}</p>

            <div class="test-buttons">
                <button onclick="testLike('${upload.id}')">Test Like</button>
                <button onclick="testComment('${upload.id}')">Test Comment</button>
            </div>

            <div id="results-${upload.id}" class="results" style="display: none;">
                <h4>Test Results:</h4>
                <pre id="output-${upload.id}"></pre>
            </div>
        </div>
    `).join('')}

    <script>
        async function testLike(uploadId) {
            console.log('Testing like for:', uploadId);
            const resultsDiv = document.getElementById('results-' + uploadId);
            const outputPre = document.getElementById('output-' + uploadId);

            resultsDiv.style.display = 'block';
            outputPre.textContent = 'Testing like...';

            try {
                const response = await fetch('/test/like/' + uploadId, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();
                outputPre.textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    console.log('Like test successful:', result);
                } else {
                    console.error('Like test failed:', result);
                }
            } catch (error) {
                console.error('Error testing like:', error);
                outputPre.textContent = 'Error: ' + error.message;
            }
        }

        async function testComment(uploadId) {
            console.log('Testing comment for:', uploadId);
            const resultsDiv = document.getElementById('results-' + uploadId);
            const outputPre = document.getElementById('output-' + uploadId);

            resultsDiv.style.display = 'block';
            outputPre.textContent = 'Testing comment...';

            try {
                const response = await fetch('/test/comment/' + uploadId, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: 'Test comment from test page' })
                });

                const result = await response.json();
                outputPre.textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    console.log('Comment test successful:', result);
                } else {
                    console.error('Comment test failed:', result);
                }
            } catch (error) {
                console.error('Error testing comment:', error);
                outputPre.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
    `;

    res.send(testPageHTML);
  } catch (error) {
    console.error('Error creating test page:', error);
    res.status(500).send('Error creating test page: ' + error.message);
  }
});

// Home route
app.get('/', (req, res) => {
  res.render('home', {
    originalUrl: req.originalUrl,
    isAuthenticated: req.isAuthenticated ? req.isAuthenticated : false,
    user: req.user || null
  });
});



// Dashboard route (temporarily public for testing) - Content loaded dynamically via API with server-side fallback
app.get('/dashboard', async (req, res) => {
  console.log('Dashboard route accessed');
  try {
    const user = req.user || { displayName: 'Guest User', uid: 'guest' };

    // Get additional user data and uploads for fallback
    try {
      const { getUserData, getUploads } = await import('./services/firebaseService.js');

      // Get user data (skip if guest)
      let userData = {};
      if (user.uid !== 'guest') {
        userData = await getUserData(user.uid) || {};
      }

      // Get uploads for server-side fallback
      const uploadsResult = await getUploads({ limit: 10 });
      const uploads = uploadsResult.uploads || [];

      console.log('Dashboard rendering with:', {
        userExists: !!user,
        uploadsCount: uploads.length,
        userDataExists: !!userData
      });

      res.render('dashboard', {
        user: user,
        userData: userData,
        uploads: uploads, // Add uploads for fallback
        originalUrl: req.originalUrl
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      res.render('dashboard', {
        user: user,
        userData: {},
        uploads: [], // Empty uploads array for fallback
        error: 'Error loading user data: ' + error.message,
        originalUrl: req.originalUrl
      });
    }
  } catch (error) {
    console.error('Error loading dashboard:', error);
    res.render('error', {
      error: 'Error loading dashboard',
      message: error.message
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});

// Keep the process alive
server.on('error', (error) => {
  console.error('Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
