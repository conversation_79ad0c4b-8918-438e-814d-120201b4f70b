// Enhanced Facebook-style Interactions

// Initialize Facebook-style interactions
document.addEventListener('DOMContentLoaded', function() {
  initializeFacebookInteractions();
  initializeTimeAgo();
  initializeCommentInputs();
});

/**
 * Initialize Facebook-style interactions
 */
function initializeFacebookInteractions() {
  // Add ripple effect to action buttons
  document.querySelectorAll('.fb-post-action-button').forEach(button => {
    button.addEventListener('click', createRippleEffect);
  });

  // Initialize like button states
  updateLikeButtonStates();

  // Initialize auto-resize for comment inputs
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    autoResizeTextarea(textarea);
  });
}

/**
 * Create ripple effect on button click
 */
function createRippleEffect(e) {
  const button = e.currentTarget;
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = e.clientX - rect.left - size / 2;
  const y = e.clientY - rect.top - size / 2;

  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
  `;

  button.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

/**
 * Handle like button click
 */
async function handleLike(postId) {
  console.log('=== LIKE FUNCTION CALLED ===');
  console.log('Like button clicked for post:', postId);
  console.log('Post ID type:', typeof postId);
  console.log('Current user ID:', window.currentUserId);

  const likeButton = document.querySelector(`[data-item-id="${postId}"].like`);
  console.log('Like button selector:', `[data-item-id="${postId}"].like`);
  console.log('Like button found:', likeButton);

  if (!likeButton) {
    console.error('Like button not found for post:', postId);
    console.log('Available like buttons:', document.querySelectorAll('.like'));
    showNotification('Like button not found', 'error');
    return;
  }

  const likeIcon = likeButton.querySelector('i');
  const likeText = likeButton.querySelector('span');
  const isLiked = likeButton.classList.contains('liked');

  console.log('Like button found:', likeButton);
  console.log('Like icon found:', likeIcon);
  console.log('Like text found:', likeText);
  console.log('Current like state:', isLiked);

  try {
    // Optimistic UI update
    if (isLiked) {
      // Unlike
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    } else {
      // Like
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';

      // Create like animation
      createLikeAnimation(likeButton);
    }

    // Make API call
    console.log('=== MAKING API CALL ===');
    console.log('API URL:', `/uploads/${postId}/like`);

    const response = await fetch(`/uploads/${postId}/like`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include'
    });

    console.log('API Response status:', response.status);
    console.log('API Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error response:', errorText);
      throw new Error(`Failed to like post: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('=== API SUCCESS ===');
    console.log('Like response:', result);

    // Update like count in stats
    const postElement = document.querySelector(`[data-post-id="${postId}"]`);
    if (postElement) {
      const likeStatsDiv = postElement.querySelector('.fb-post-stats div:first-child');
      if (likeStatsDiv) {
        if (result.likeCount > 0) {
          likeStatsDiv.innerHTML = `<i class="bi bi-hand-thumbs-up-fill"></i><span>${result.likeCount}</span>`;
        } else {
          likeStatsDiv.innerHTML = '';
        }
      }
    }

    // Show success notification
    showNotification(result.liked ? 'Post liked!' : 'Post unliked!', 'success');

    // Update button state based on server response
    if (result.liked) {
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';
    } else {
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    }

  } catch (error) {
    console.error('Error liking post:', error);

    // Revert optimistic update on error
    if (isLiked) {
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';
    } else {
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    }

    // Show error message
    showNotification('Failed to like post. Please try again.', 'error');
  }
}

/**
 * Toggle like button state
 */
function toggleLikeButton(button, isLiked) {
  if (isLiked) {
    button.classList.add('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up-fill';
  } else {
    button.classList.remove('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up';
  }
}

/**
 * Update like count display
 */
function updateLikeCount(postId, increment) {
  const statsDiv = document.querySelector(`[data-post-id="${postId}"] .fb-post-stats > div:first-child`);
  const countSpan = statsDiv.querySelector('span');
  
  if (countSpan) {
    const currentCount = parseInt(countSpan.textContent) || 0;
    const newCount = increment ? currentCount + 1 : Math.max(0, currentCount - 1);
    
    if (newCount > 0) {
      countSpan.textContent = newCount;
      if (!statsDiv.querySelector('i')) {
        statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>' + newCount + '</span>';
      }
    } else {
      statsDiv.innerHTML = '';
    }
  } else if (increment) {
    statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>1</span>';
  }
}

/**
 * Create like animation
 */
function createLikeAnimation(button) {
  const animation = document.createElement('div');
  animation.className = 'like-animation';
  animation.innerHTML = '<i class="bi bi-heart-fill" style="color: #e74c3c; font-size: 24px;"></i>';
  
  button.style.position = 'relative';
  button.appendChild(animation);
  
  setTimeout(() => {
    animation.remove();
  }, 700);
}

/**
 * Focus comment input
 */
function focusCommentInput(postId) {
  const commentInput = document.getElementById(`comment-text-${postId}`);
  if (commentInput) {
    commentInput.focus();
    commentInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

/**
 * Handle comment submission
 */
async function submitComment(postId) {
  console.log('=== COMMENT FUNCTION CALLED ===');
  console.log('Submitting comment for post:', postId);
  console.log('Post ID type:', typeof postId);
  console.log('Current user ID:', window.currentUserId);

  const textarea = document.getElementById(`comment-text-${postId}`);
  console.log('Comment textarea selector:', `comment-text-${postId}`);
  console.log('Comment textarea found:', textarea);

  if (!textarea) {
    console.error('Comment textarea not found for post:', postId);
    console.log('Available textareas:', document.querySelectorAll('textarea'));
    showNotification('Comment input not found', 'error');
    return;
  }

  const commentText = textarea.value.trim();
  console.log('Comment textarea found:', textarea);
  console.log('Comment text:', commentText);

  if (!commentText) {
    console.log('No comment text, aborting');
    showNotification('Please enter a comment', 'error');
    return;
  }

  try {
    // Show loading state
    const sendBtn = textarea.parentElement.querySelector('.fb-comment-send-btn');
    if (sendBtn) {
      const originalIcon = sendBtn.innerHTML;
      sendBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
      sendBtn.disabled = true;
    }

    console.log('=== MAKING COMMENT API CALL ===');
    console.log('API URL:', `/uploads/${postId}/comments`);
    console.log('Comment text to send:', commentText);

    // Submit comment to API
    const response = await fetch(`/uploads/${postId}/comments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        text: commentText
      })
    });

    console.log('Comment API response status:', response.status);
    console.log('Comment API response ok:', response.ok);

    if (response.ok) {
      const result = await response.json();
      console.log('Comment API response:', result);

      // Clear the textarea
      textarea.value = '';
      autoResizeTextarea(textarea);

      // Add the new comment to the UI
      addCommentToUI(postId, result.comment);

      // Update comment count with the actual count from server
      updateCommentCount(postId, result.commentCount || 1);

      // Show success feedback
      showNotification('Comment posted!', 'success');

    } else {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error('Failed to post comment: ' + response.status);
    }
  } catch (error) {
    console.error('Error posting comment:', error);
    showNotification('Failed to post comment. Please try again.', 'error');
  } finally {
    // Reset button state
    const sendBtn = textarea.parentElement.querySelector('.fb-comment-send-btn');
    if (sendBtn) {
      sendBtn.innerHTML = '<i class="bi bi-send"></i>';
      sendBtn.disabled = false;
    }
  }
}

/**
 * Handle comment keydown (Enter to submit)
 */
function handleCommentKeydown(event, postId) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    submitComment(postId);
  }
}

/**
 * Auto-resize textarea
 */
function autoResizeTextarea(textarea) {
  textarea.style.height = 'auto';
  textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
}

/**
 * Initialize comment inputs
 */
function initializeCommentInputs() {
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    textarea.addEventListener('input', function() {
      autoResizeTextarea(this);
    });
  });
}

/**
 * Add comment to UI
 */
function addCommentToUI(postId, comment) {
  console.log('Adding comment to UI:', postId, comment);

  const commentsSection = document.getElementById(`comments-${postId}`);
  if (!commentsSection) {
    console.error('Comments section not found for post:', postId);
    return;
  }

  let existingComments = commentsSection.querySelector('.fb-existing-comments');

  if (!existingComments) {
    existingComments = document.createElement('div');
    existingComments.className = 'fb-existing-comments';
    commentsSection.insertBefore(existingComments, commentsSection.querySelector('.fb-comment-input-section'));
  }

  const commentHTML = `
    <div class="fb-comment" data-comment-id="${comment.id}">
      <div class="fb-comment-avatar">
        ${comment.userPhotoURL ?
          `<img src="${comment.userPhotoURL}" alt="${comment.userName}" class="fb-comment-avatar-img">` :
          `<div class="fb-comment-avatar-placeholder">
            <i class="bi bi-person-fill"></i>
          </div>`
        }
      </div>
      <div class="fb-comment-content">
        <div class="fb-comment-bubble">
          <div class="fb-comment-author">
            <a href="/profile/user/${comment.userId}" class="fb-comment-author-link">
              ${comment.userName}
            </a>
          </div>
          <div class="fb-comment-text">${comment.text}</div>
        </div>
        <div class="fb-comment-actions">
          <span class="fb-comment-time">Just now</span>
          <span class="fb-comment-action" onclick="likeComment('${comment.id}')">Like</span>
          <span class="fb-comment-action" onclick="replyToComment('${comment.id}')">Reply</span>
        </div>
      </div>
    </div>
  `;

  existingComments.insertAdjacentHTML('beforeend', commentHTML);
  console.log('Comment added to UI successfully');
}

/**
 * Update comment count
 */
function updateCommentCount(postId, newCount) {
  console.log('Updating comment count for post:', postId, 'New count:', newCount);

  // Find the post by data-post-id attribute
  const postElement = document.querySelector(`[data-post-id="${postId}"]`);
  if (!postElement) {
    console.error('Post element not found for ID:', postId);
    return;
  }

  // Find the stats div (second div in fb-post-stats)
  const statsDiv = postElement.querySelector('.fb-post-stats div:last-child');
  if (!statsDiv) {
    console.error('Stats div not found for post:', postId);
    return;
  }

  // Update the comment count display
  if (newCount > 0) {
    const commentText = newCount === 1 ? 'comment' : 'comments';
    statsDiv.innerHTML = `${newCount} ${commentText}`;
  } else {
    statsDiv.innerHTML = '';
  }

  console.log('Comment count updated successfully');
}

/**
 * Handle share button click
 */
function handleShare(postId) {
  // Create share modal or use Web Share API
  if (navigator.share) {
    const post = document.querySelector(`[data-post-id="${postId}"]`);
    const title = post.querySelector('.fb-post-text h5')?.textContent || 'Sustainable Farming Post';
    const text = post.querySelector('.fb-post-text p')?.textContent || '';
    
    navigator.share({
      title: title,
      text: text,
      url: window.location.href + '#post-' + postId
    }).catch(console.error);
  } else {
    // Fallback: Copy link to clipboard
    const url = window.location.href + '#post-' + postId;
    navigator.clipboard.writeText(url).then(() => {
      showNotification('Link copied to clipboard!', 'success');
    }).catch(() => {
      showNotification('Failed to copy link', 'error');
    });
  }
}

/**
 * Initialize time ago functionality
 */
function initializeTimeAgo() {
  updateTimeAgo();
  setInterval(updateTimeAgo, 60000); // Update every minute
}

/**
 * Update time ago displays
 */
function updateTimeAgo() {
  document.querySelectorAll('.time-ago').forEach(element => {
    const time = element.getAttribute('data-time');
    if (time) {
      element.textContent = getTimeAgo(new Date(time));
    }
  });
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
  
  return date.toLocaleDateString();
}

/**
 * Show notification - wrapper for toast system
 */
function showNotification(message, type = 'info') {
  if (typeof toast !== 'undefined') {
    // Use the toast notification system
    switch(type) {
      case 'success':
        toast.success(message);
        break;
      case 'error':
        toast.error(message);
        break;
      case 'warning':
        toast.warning(message);
        break;
      case 'info':
      default:
        toast.info(message);
        break;
    }
  } else {
    // Fallback to console if toast is not available
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// Export functions to global scope
window.handleLike = handleLike;
window.submitComment = submitComment;
window.focusCommentInput = focusCommentInput;
window.handleCommentKeydown = handleCommentKeydown;
window.autoResizeTextarea = autoResizeTextarea;
window.handleShare = handleShare;
window.showNotification = showNotification;
