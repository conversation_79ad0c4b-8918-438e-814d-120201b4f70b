/**
 * Content Manager - Handles consistent content display for any amount of content
 * Supports infinite scroll, virtual scrolling, and performance optimization
 */
class ContentManager {
    constructor(options = {}) {
        this.container = options.container || document.getElementById('content-container');
        this.loadingIndicator = options.loadingIndicator || document.getElementById('loading-indicator');
        this.itemsPerPage = options.itemsPerPage || 10;
        this.currentPage = 1;
        this.isLoading = false;
        this.hasMoreContent = true;
        this.totalItems = 0;
        this.loadedItems = [];
        this.apiEndpoint = options.apiEndpoint || '/api/content';
        this.filter = options.filter || 'all';

        // Virtual scrolling settings
        this.itemHeight = options.itemHeight || 400; // Estimated height per item
        this.bufferSize = options.bufferSize || 5; // Items to render outside viewport
        this.viewportHeight = window.innerHeight;
        this.scrollTop = 0;

        // Performance optimization
        this.throttleDelay = options.throttleDelay || 100;
        this.lastScrollTime = 0;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialContent();
        this.setupIntersectionObserver();
    }

    setupEventListeners() {
        // Throttled scroll listener for performance
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, this.throttleDelay));

        // Filter change listener
        document.addEventListener('filterChange', (e) => {
            this.changeFilter(e.detail.filter);
        });

        // Resize listener for responsive layout
        window.addEventListener('resize', this.throttle(() => {
            this.handleResize();
        }, this.throttleDelay));

        // Refresh listener
        document.addEventListener('refreshContent', () => {
            this.refresh();
        });
    }

    setupIntersectionObserver() {
        // Use Intersection Observer for better performance
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.target === this.loadingIndicator) {
                    this.loadMoreContent();
                }
            });
        }, {
            rootMargin: '100px' // Start loading 100px before the indicator is visible
        });

        if (this.loadingIndicator) {
            this.observer.observe(this.loadingIndicator);
        }
    }

    async loadInitialContent() {
        this.showLoadingState();
        try {
            const response = await this.fetchContent(1, this.itemsPerPage);
            this.loadedItems = response.items || [];
            this.totalItems = response.total || 0;
            this.hasMoreContent = response.hasMore !== false;
            this.renderContent();
            this.hideLoadingState();
        } catch (error) {
            this.showErrorState(error);
        }
    }

    async loadMoreContent() {
        if (this.isLoading || !this.hasMoreContent) return;

        this.isLoading = true;
        this.showLoadingIndicator();

        try {
            const nextPage = this.currentPage + 1;
            const response = await this.fetchContent(nextPage, this.itemsPerPage);

            if (response.items && response.items.length > 0) {
                this.loadedItems = [...this.loadedItems, ...response.items];
                this.currentPage = nextPage;
                this.hasMoreContent = response.hasMore !== false;
                this.appendContent(response.items);
            } else {
                this.hasMoreContent = false;
                this.showEndMessage();
            }
        } catch (error) {
            this.showErrorState(error);
        } finally {
            this.isLoading = false;
            this.hideLoadingIndicator();
        }
    }

    async fetchContent(page, limit) {
        const params = new URLSearchParams({
            page: page,
            limit: limit,
            filter: this.filter
        });

        console.log('Fetching content:', `${this.apiEndpoint}?${params}`);
        const response = await fetch(`${this.apiEndpoint}?${params}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Fetched data:', data);

        // Transform the response to match expected format
        return {
            items: data.uploads || [],
            total: data.pagination?.total || 0,
            hasMore: data.pagination?.hasMore || false,
            page: data.pagination?.page || page
        };
    }

    renderContent() {
        if (!this.container) return;

        if (this.loadedItems.length === 0) {
            this.showEmptyState();
            return;
        }

        // Clear container
        this.container.innerHTML = '';

        // Create content grid
        const contentGrid = this.createContentGrid();
        this.container.appendChild(contentGrid);

        // Render items
        this.loadedItems.forEach((item, index) => {
            const itemElement = this.createContentItem(item, index);
            contentGrid.appendChild(itemElement);
        });

        // Add loading indicator
        if (this.hasMoreContent) {
            this.container.appendChild(this.createLoadingIndicator());
        }
    }

    appendContent(newItems) {
        const contentGrid = this.container.querySelector('.content-grid') || this.createContentGrid();

        newItems.forEach((item, index) => {
            const itemElement = this.createContentItem(item, this.loadedItems.length - newItems.length + index);
            itemElement.classList.add('fade-in');
            contentGrid.appendChild(itemElement);
        });
    }

    createContentGrid() {
        const grid = document.createElement('div');
        grid.className = 'content-grid';
        grid.style.cssText = `
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px 0;
        `;
        return grid;
    }

    createContentItem(item, index) {
        const itemElement = document.createElement('div');
        itemElement.className = 'content-item fb-post';
        itemElement.dataset.itemId = item.id;
        itemElement.dataset.postId = item.id;  // Add data-post-id for consistency
        itemElement.dataset.index = index;

        itemElement.innerHTML = `
            <div class="fb-post-header">
                <div class="fb-post-avatar">
                    <a href="/profile/user/${item.userId}" class="fb-avatar-link" title="View ${item.userName || 'User'}'s profile">
                        ${item.userPhotoURL ?
                            `<img src="${item.userPhotoURL}" alt="${item.userName || 'User'}" class="fb-avatar">` :
                            `<div class="fb-avatar fb-avatar-placeholder">
                                <i class="bi bi-person-fill"></i>
                            </div>`
                        }
                    </a>
                </div>
                <div class="fb-post-user-info">
                    <div class="fb-post-username">
                        <a href="/profile/user/${item.userId}" class="text-decoration-none" style="color: var(--fb-black); font-weight: 600;" title="View ${item.userName || 'User'}'s profile">
                            ${item.userName || 'Anonymous'}
                        </a>
                    </div>
                    <div class="fb-post-time">
                        ${new Date(item.createdAt).toLocaleDateString()} ·
                        <span class="badge bg-light text-dark">${item.category || 'General'}</span>
                    </div>
                </div>
                <div class="fb-post-options">
                    <i class="bi bi-three-dots"></i>
                </div>
            </div>

            <div class="fb-post-content">
                <div class="fb-post-text">
                    <h5>${item.title || 'Untitled'}</h5>
                    <p>${item.description || ''}</p>
                </div>
                ${this.renderMediaContent(item)}
            </div>

            <div class="fb-post-stats">
                <div>
                    <i class="bi bi-hand-thumbs-up-fill"></i> ${item.likeCount || 0}
                </div>
                <div>
                    ${item.commentCount || 0} comments · ${item.shareCount || 0} shares
                </div>
            </div>

            <div class="fb-post-actions">
                ${this.renderLikeButton(item)}
                <div class="fb-post-action-button comment" data-item-id="${item.id}" onclick="focusCommentInput('${item.id}')">
                    <i class="bi bi-chat"></i>
                    <span>Comment</span>
                </div>
                <div class="fb-post-action-button share" data-item-id="${item.id}" onclick="handleShare('${item.id}')">
                    <i class="bi bi-share"></i>
                    <span>Share</span>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="fb-comments-section" id="comments-${item.id}">
                <!-- Existing Comments -->
                ${this.renderExistingComments(item)}

                <!-- Comment Input Box (Always Visible) -->
                <div class="fb-comment-input-section" id="comment-input-${item.id}">
                    <div class="fb-comment-input-container">
                        <div class="fb-comment-input-avatar">
                            <div class="fb-comment-avatar-placeholder">
                                <i class="bi bi-person-fill"></i>
                            </div>
                        </div>
                        <div class="fb-comment-input-wrapper">
                            <textarea
                                class="fb-comment-input"
                                id="comment-text-${item.id}"
                                placeholder="Write a comment..."
                                rows="1"
                                onkeydown="handleCommentKeydown(event, '${item.id}')"
                                oninput="autoResizeTextarea(this)"
                            ></textarea>
                            <div class="fb-comment-input-actions">
                                <button type="button" class="fb-comment-emoji-btn" title="Add emoji">
                                    <i class="bi bi-emoji-smile"></i>
                                </button>
                                <button type="button" class="fb-comment-photo-btn" title="Add photo">
                                    <i class="bi bi-camera"></i>
                                </button>
                                <button
                                    type="button"
                                    class="fb-comment-send-btn"
                                    onclick="submitComment('${item.id}')"
                                    title="Send comment"
                                >
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        this.attachItemEventListeners(itemElement, item);

        return itemElement;
    }

    renderExistingComments(item) {
        if (!item.comments || item.comments.length === 0) {
            return '';
        }

        const commentsHTML = item.comments.map(comment => `
            <div class="fb-comment">
                <div class="fb-comment-avatar">
                    ${comment.userPhotoURL ?
                        `<img src="${comment.userPhotoURL}" alt="${comment.userName}" class="fb-comment-avatar-img">` :
                        `<div class="fb-comment-avatar-placeholder">
                            <i class="bi bi-person-fill"></i>
                        </div>`
                    }
                </div>
                <div class="fb-comment-content">
                    <div class="fb-comment-bubble">
                        <div class="fb-comment-author">
                            <a href="/profile/user/${comment.userId}" class="fb-comment-author-link">
                                ${comment.userName}
                            </a>
                        </div>
                        <div class="fb-comment-text">${comment.text}</div>
                    </div>
                    <div class="fb-comment-actions">
                        <span class="fb-comment-time">${new Date(comment.createdAt).toLocaleDateString()}</span>
                        <span class="fb-comment-action">Like</span>
                        <span class="fb-comment-action">Reply</span>
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="fb-existing-comments">
                ${commentsHTML}
            </div>
        `;
    }

    renderLikeButton(item) {
        // Check if current user has liked this post
        // Note: We'll need to get current user info from global variables
        const currentUserId = window.currentUserId || null;
        const likesArray = Array.isArray(item.likes) ? item.likes : [];
        const isLiked = currentUserId && likesArray.includes(currentUserId);

        return `
            <div class="fb-post-action-button like ${isLiked ? 'liked' : ''}"
                 data-item-id="${item.id}"
                 onclick="handleLike('${item.id}')"
                 style="${isLiked ? 'color: #1877f2;' : ''}">
                <i class="bi bi-hand-thumbs-up${isLiked ? '-fill' : ''}"></i>
                <span>${isLiked ? 'Liked' : 'Like'}</span>
            </div>
        `;
    }

    renderMediaContent(item) {
        if (!item.fileUrl) return '';

        const isImage = this.isImageFile(item.fileUrl, item.fileType);
        const isVideo = this.isVideoFile(item.fileUrl, item.fileType);
        const isPdf = this.isPdfFile(item.fileUrl, item.fileType);

        if (isImage) {
            return `<img src="${item.fileUrl}" class="fb-post-image" alt="${item.title}" loading="lazy">`;
        } else if (isVideo) {
            return `
                <div class="fb-post-file-container video">
                    <i class="bi bi-film"></i>
                    <div>Video Content</div>
                    <a href="${item.fileUrl}" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
                        <i class="bi bi-play-fill"></i> Play Video
                    </a>
                </div>
            `;
        } else if (isPdf) {
            return `
                <div class="fb-post-file-container pdf">
                    <i class="bi bi-file-pdf"></i>
                    <div>PDF Document</div>
                    <a href="${item.fileUrl}" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
                        <i class="bi bi-eye"></i> View PDF
                    </a>
                </div>
            `;
        } else {
            return `
                <div class="fb-post-file-container file">
                    <i class="bi bi-file-earmark"></i>
                    <div>Document</div>
                    <a href="${item.fileUrl}" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
                        <i class="bi bi-download"></i> Download
                    </a>
                </div>
            `;
        }
    }

    attachItemEventListeners(itemElement, item) {
        // Note: Like, comment, and share handlers are now managed by facebook-interactions.js
        // This prevents conflicts and ensures consistent behavior across all posts

        // The onclick attributes in the HTML will handle the interactions
        // No additional event listeners needed here
    }

    // Utility functions
    isImageFile(url, type) {
        if (type) return type.startsWith('image/');
        const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
        return imageExts.some(ext => url.toLowerCase().includes(ext));
    }

    isVideoFile(url, type) {
        if (type) return type.startsWith('video/');
        const videoExts = ['.mp4', '.webm', '.ogg'];
        return videoExts.some(ext => url.toLowerCase().includes(ext));
    }

    isPdfFile(url, type) {
        if (type) return type === 'application/pdf';
        return url.toLowerCase().includes('.pdf');
    }

    // Event handlers
    handleScroll() {
        this.scrollTop = window.pageYOffset;

        // Check if near bottom for infinite scroll
        const scrollHeight = document.documentElement.scrollHeight;
        const scrollTop = window.pageYOffset;
        const clientHeight = window.innerHeight;

        if (scrollTop + clientHeight >= scrollHeight - 1000) {
            this.loadMoreContent();
        }
    }

    handleResize() {
        this.viewportHeight = window.innerHeight;
        // Recalculate visible items if using virtual scrolling
    }

    // Like, comment, and share handling is now managed by facebook-interactions.js
    // This prevents conflicts and ensures consistent behavior

    // State management
    showLoadingState() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="loading-state text-center py-5">
                    <div class="spinner-border text-success mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h4>Loading content...</h4>
                    <p class="text-muted">Please wait while we fetch the latest posts</p>
                </div>
            `;
        }
    }

    hideLoadingState() {
        const loadingState = this.container?.querySelector('.loading-state');
        if (loadingState) {
            loadingState.remove();
        }
    }

    showLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'block';
        }
    }

    hideLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
        }
    }

    showEmptyState() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="empty-state text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-cloud-upload text-muted" style="font-size: 5rem;"></i>
                    </div>
                    <h3>No Content Available</h3>
                    <p class="text-muted mb-4">Be the first to share content with the community!</p>
                    <a href="/uploads/new" class="btn btn-lg btn-success">
                        <i class="bi bi-cloud-upload me-2"></i> Upload Content
                    </a>
                </div>
            `;
        }
    }

    showErrorState(error) {
        if (this.container) {
            this.container.innerHTML = `
                <div class="error-state text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 5rem;"></i>
                    </div>
                    <h3>Error Loading Content</h3>
                    <p class="text-muted mb-4">${error.message || 'Something went wrong. Please try again.'}</p>
                    <button class="btn btn-primary" onclick="contentManager.refresh()">
                        <i class="bi bi-arrow-clockwise me-2"></i> Try Again
                    </button>
                </div>
            `;
        }
    }

    showEndMessage() {
        const endMessage = document.createElement('div');
        endMessage.className = 'end-message text-center py-4';
        endMessage.innerHTML = `
            <div class="text-muted">
                <i class="bi bi-check-circle me-2"></i>
                You've reached the end! No more content to load.
            </div>
        `;
        this.container.appendChild(endMessage);
    }

    // Filter management
    async changeFilter(newFilter) {
        if (this.filter === newFilter) return;

        this.filter = newFilter;
        this.currentPage = 1;
        this.loadedItems = [];
        this.hasMoreContent = true;

        await this.loadInitialContent();
    }

    // Refresh functionality
    async refresh() {
        this.currentPage = 1;
        this.loadedItems = [];
        this.hasMoreContent = true;
        await this.loadInitialContent();
    }

    // Utility function for throttling
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        return function (...args) {
            const currentTime = Date.now();

            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }

    // Cleanup
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }

        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
    }
}

// Export for use in other scripts
window.ContentManager = ContentManager;
