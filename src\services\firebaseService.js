import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc
} from 'firebase/firestore';
import {
  ref,
  uploadString,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { auth, db, storage } from '../config/initFirebase.js';

// Polyfill localStorage for Node.js environment
if (typeof localStorage === 'undefined' || localStorage === null) {
  let store = {};
  global.localStorage = {
    getItem: function(key) {
      return store[key] || null;
    },
    setItem: function(key, value) {
      store[key] = value.toString();
    },
    removeItem: function(key) {
      delete store[key];
    },
    clear: function() {
      store = {};
    }
  };
}

// Collections
const USERS_COLLECTION = 'users';
const UPLOADS_COLLECTION = 'uploads';

// User Authentication Functions

// Register a new user
export const registerUser = async (userData) => {
  try {
    // Check if Firebase Auth is properly configured
    if (!auth) {
      throw new Error('Firebase Authentication is not initialized');
    }

    // For development/testing purposes, we'll use a fallback to local storage if Firebase Auth fails
    try {
      // Try to create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      const user = userCredential.user;

      // Update profile with display name
      await updateProfile(user, {
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`
      });

      // Store additional user data in Firestore
      const userDocRef = doc(db, USERS_COLLECTION, user.uid);
      await setDoc(userDocRef, {
        email: userData.email,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
        firstName: userData.firstName,
        lastName: userData.lastName,
        farmName: userData.farmName || '',
        location: userData.location || '',
        bio: userData.bio || '',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return user;
    } catch (firebaseError) {
      console.error('Firebase Auth error:', firebaseError);
      throw firebaseError;
    }
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

// Login user
export const loginUser = async (email, password) => {
  try {
    // Check if Firebase Auth is properly configured
    if (!auth) {
      throw new Error('Firebase Authentication is not initialized');
    }

    // Sign in with Firebase Auth
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

// Logout user
export const logoutUser = async () => {
  try {
    await signOut(auth);
    return true;
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};

// Get current user
export const getCurrentUser = () => {
  return auth.currentUser;
};

// Check if user is authenticated
export const isAuthenticated = () => {
  return !!auth.currentUser;
};

// Get all users
export const getUsers = async () => {
  try {
    const usersQuery = query(collection(db, USERS_COLLECTION));
    const usersSnapshot = await getDocs(usersQuery);

    return usersSnapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
};

// Get user data from Firestore
export const getUserData = async (userId) => {
  try {
    const userDocRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      return userDoc.data();
    }

    return null;
  } catch (error) {
    console.error('Error getting user data:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (userData) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    // Update display name if provided
    if (userData.displayName) {
      await updateProfile(user, {
        displayName: userData.displayName
      });
    }

    // Update user data in Firestore
    const userDocRef = doc(db, USERS_COLLECTION, user.uid);
    await updateDoc(userDocRef, {
      ...userData,
      updatedAt: serverTimestamp()
    });

    return user;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Change user password
export const changeUserPassword = async (currentPassword, newPassword) => {
  try {
    const user = auth.currentUser;

    if (!user) {
      throw new Error('No authenticated user');
    }

    // Re-authenticate user before changing password
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // Update password
    await updatePassword(user, newPassword);

    return true;
  } catch (error) {
    console.error('Error changing password:', error);
    throw error;
  }
};

// Update profile picture
export const updateProfilePicture = async (userId, photoDataUrl) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    if (user.uid !== userId) {
      throw new Error('You can only update your own profile picture');
    }

    // Generate a unique filename based on timestamp
    const timestamp = Date.now();
    const filename = `profile_${timestamp}.jpg`;

    // Create a reference to the file in Firebase Storage
    const fileRef = ref(storage, `profile_pictures/${userId}/${filename}`);

    // Upload the file to Firebase Storage
    await uploadString(fileRef, photoDataUrl, 'data_url');

    // Get the download URL
    const photoURL = await getDownloadURL(fileRef);

    // Update Firebase Auth profile
    await updateProfile(user, { photoURL });

    // Update user data in Firestore
    const userDocRef = doc(db, USERS_COLLECTION, userId);
    await updateDoc(userDocRef, {
      photoURL,
      updatedAt: serverTimestamp()
    });

    return photoURL;
  } catch (error) {
    console.error('Error updating profile picture:', error);
    throw error;
  }
};

// Content Upload Functions

// Get all uploads with pagination support
export const getUploads = async (options = {}) => {
  try {
    const {
      page = 1,
      limit: limitCount = 10,
      filter = 'all',
      skip = 0
    } = options;

    const uploads = [];
    let total = 0;
    let hasMore = false;

    // Try to get uploads from Firestore
    try {
      // Build query based on filter
      let uploadsQuery = collection(db, UPLOADS_COLLECTION);

      // Apply category filter if not 'all'
      if (filter !== 'all') {
        uploadsQuery = query(uploadsQuery, where('category', '==', filter));
      }

      // Add ordering and pagination
      uploadsQuery = query(
        uploadsQuery,
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1) // Get one extra to check if there are more
      );

      const querySnapshot = await getDocs(uploadsQuery);

      // Get total count for pagination info
      const countQuery = filter !== 'all'
        ? query(collection(db, UPLOADS_COLLECTION), where('category', '==', filter))
        : collection(db, UPLOADS_COLLECTION);

      const countSnapshot = await getDocs(countQuery);
      total = countSnapshot.size;

      // Process results
      const docs = querySnapshot.docs;
      hasMore = docs.length > limitCount;

      // Remove the extra document if we have more
      if (hasMore) {
        docs.pop();
      }

      docs.forEach((doc) => {
        const data = doc.data();
        uploads.push({
          id: doc.id,
          ...data,
          // Convert Firestore timestamps to JavaScript dates
          createdAt: data.createdAt?.toDate?.() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.() || data.updatedAt
        });
      });

      return {
        uploads,
        total,
        hasMore,
        page,
        limit: limitCount
      };
    } catch (firestoreError) {
      console.error('Firestore error getting uploads:', firestoreError);
      throw firestoreError;
    }
  } catch (error) {
    console.error('Error getting uploads:', error);
    return {
      uploads: [],
      total: 0,
      hasMore: false,
      page,
      limit: limitCount
    };
  }
};

// Get upload by ID
export const getUploadById = async (uploadId) => {
  try {
    const uploadDocRef = doc(db, UPLOADS_COLLECTION, uploadId);
    const uploadDoc = await getDoc(uploadDocRef);

    if (uploadDoc.exists()) {
      return {
        id: uploadDoc.id,
        ...uploadDoc.data()
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting upload:', error);
    return null;
  }
};

// Get uploads by user
export const getUploadsByUser = async (userId) => {
  try {
    const uploads = [];

    const uploadsQuery = query(
      collection(db, UPLOADS_COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(uploadsQuery);

    querySnapshot.forEach((doc) => {
      uploads.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return uploads;
  } catch (error) {
    console.error('Error getting user uploads:', error);
    return []; // Return empty array instead of throwing
  }
};

// Add new upload
export const addUpload = async (uploadData) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to add content');
    }

    let fileUrl = uploadData.fileUrl;
    let uploadId = null;
    let fileType = '';

    // Determine file type from data URL
    if (fileUrl && fileUrl.startsWith('data:')) {
      const mimeTypeMatch = fileUrl.match(/^data:([^;]+);/);
      if (mimeTypeMatch) {
        fileType = mimeTypeMatch[1];
        console.log('Detected file type:', fileType);
      }
    }

    // Generate a unique filename based on timestamp and file type
    const timestamp = Date.now();
    const fileExtension = getFileExtensionFromMimeType(fileType);
    const filename = `${timestamp}${fileExtension ? '.' + fileExtension : ''}`;

    console.log('Uploading file with name:', filename);

    // Try to use Firebase
    try {
      // If the fileUrl is a data URL, upload it to Firebase Storage
      if (fileUrl && fileUrl.startsWith('data:')) {
        try {
          console.log('Uploading to Firebase Storage...');

          // Create a reference to the file in Firebase Storage
          const fileRef = ref(storage, `uploads/${user.uid}/${filename}`);

          // Upload the file to Firebase Storage
          await uploadString(fileRef, fileUrl, 'data_url');
          console.log('File uploaded to Firebase Storage');

          // Get the download URL
          fileUrl = await getDownloadURL(fileRef);
          console.log('Download URL obtained:', fileUrl);
        } catch (storageError) {
          console.error('Firebase Storage error:', storageError);
          console.log('Using data URL as fallback');
          // Keep the original data URL if Firebase Storage fails
        }
      }

      console.log('Adding document to Firestore...');

      // Get user data to include profile picture
      let userPhotoURL = null;
      try {
        const userData = await getUserData(user.uid);
        if (userData && userData.photoURL) {
          userPhotoURL = userData.photoURL;
        }
      } catch (userDataError) {
        console.error('Error getting user data for upload:', userDataError);
        // Continue without the photo URL
      }

      // Add upload to Firestore with likes and comments arrays
      const uploadRef = await addDoc(collection(db, UPLOADS_COLLECTION), {
        title: uploadData.title,
        description: uploadData.description,
        category: uploadData.category,
        fileUrl: fileUrl,
        fileType: fileType,
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userPhotoURL: userPhotoURL,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        likes: [],
        likeCount: 0,
        comments: [],
        commentCount: 0
      });

      uploadId = uploadRef.id;
      console.log('Document added to Firestore with ID:', uploadId);

      // Get the new upload with ID
      const newUpload = await getUploadById(uploadId);

      return newUpload;
    } catch (firebaseError) {
      console.error('Firebase error adding upload:', firebaseError);
      throw firebaseError;
    }
  } catch (error) {
    console.error('Error adding upload:', error);
    throw error;
  }
};

// Helper function to get file extension from MIME type
function getFileExtensionFromMimeType(mimeType) {
  const mimeToExt = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'image/svg+xml': 'svg',
    'video/mp4': 'mp4',
    'video/webm': 'webm',
    'video/ogg': 'ogg',
    'application/pdf': 'pdf',
    'text/plain': 'txt',
    'application/json': 'json'
  };

  return mimeToExt[mimeType] || '';
}

// Like an upload
export const likeUpload = async (uploadId, user = null) => {
  try {
    // Get current user (from parameter or getCurrentUser for client-side)
    const currentUser = user || getCurrentUser();
    console.log('Current user in likeUpload:', currentUser ? currentUser.uid : 'No user');

    if (!currentUser) {
      throw new Error('You must be logged in to like content');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);
    console.log('Upload found:', upload ? 'Yes' : 'No');

    if (!upload) {
      throw new Error('Content not found');
    }

    // Get the upload document reference
    const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

    // Check if user already liked this upload
    const likes = upload.likes || [];
    const userLiked = likes.includes(currentUser.uid);
    console.log('User already liked this upload:', userLiked);
    console.log('Current likes:', likes);

    if (userLiked) {
      // Unlike: Remove user from likes array
      const newLikes = likes.filter(uid => uid !== currentUser.uid);
      const newLikeCount = Math.max(0, (upload.likeCount || likes.length) - 1);

      console.log('Unliking upload. New likes:', newLikes);
      console.log('New like count:', newLikeCount);

      await updateDoc(uploadRef, {
        likes: newLikes,
        likeCount: newLikeCount,
        updatedAt: serverTimestamp()
      });
      console.log('Upload unliked successfully');
    } else {
      // Like: Add user to likes array
      const newLikes = [...likes, currentUser.uid];
      const newLikeCount = (upload.likeCount || likes.length) + 1;

      console.log('Liking upload. New likes:', newLikes);
      console.log('New like count:', newLikeCount);

      await updateDoc(uploadRef, {
        likes: newLikes,
        likeCount: newLikeCount,
        updatedAt: serverTimestamp()
      });
      console.log('Upload liked successfully');
    }

    // Return the updated upload
    const updatedUpload = await getUploadById(uploadId);
    console.log('Updated upload retrieved:', updatedUpload ? 'Yes' : 'No');
    return updatedUpload;
  } catch (error) {
    console.error('Error liking upload:', error);
    throw error;
  }
};

// Add a comment to an upload
export const addComment = async (uploadId, commentText, user = null) => {
  try {
    // Get current user (from parameter or getCurrentUser for client-side)
    const currentUser = user || getCurrentUser();

    if (!currentUser) {
      throw new Error('You must be logged in to comment');
    }

    if (!commentText || commentText.trim() === '') {
      throw new Error('Comment cannot be empty');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    // Get user profile picture if available
    let userPhotoURL = null;
    try {
      const userData = await getUserData(currentUser.uid);
      if (userData && userData.photoURL) {
        userPhotoURL = userData.photoURL;
      }
    } catch (error) {
      console.error('Error getting user photo for comment:', error);
      // Continue without the photo URL
    }

    // Create comment object
    const comment = {
      id: Date.now().toString(),
      text: commentText.trim(),
      userId: currentUser.uid,
      userName: currentUser.displayName || 'Anonymous',
      userPhotoURL: userPhotoURL || currentUser.photoURL || null,
      createdAt: new Date().toISOString()
    };

    // Get the upload document reference
    const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

    // Add comment to comments array
    const comments = upload.comments || [];

    await updateDoc(uploadRef, {
      comments: [...comments, {
        ...comment,
        createdAt: serverTimestamp()
      }],
      commentCount: (upload.commentCount || comments.length) + 1,
      updatedAt: serverTimestamp()
    });

    console.log('Comment added');

    // Return the updated upload
    return await getUploadById(uploadId);
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

// Delete a comment from an upload
export const deleteComment = async (uploadId, commentId) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to delete a comment');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    // Get the upload document reference
    const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

    // Find the comment
    const comments = upload.comments || [];
    const comment = comments.find(c => c.id === commentId);

    if (!comment) {
      throw new Error('Comment not found');
    }

    // Check if user is the comment author or the upload owner
    if (comment.userId !== user.uid && upload.userId !== user.uid) {
      throw new Error('You can only delete your own comments');
    }

    // Remove comment from comments array
    const updatedComments = comments.filter(c => c.id !== commentId);

    await updateDoc(uploadRef, {
      comments: updatedComments,
      commentCount: (upload.commentCount || comments.length) - 1,
      updatedAt: serverTimestamp()
    });

    console.log('Comment deleted');

    // Return the updated upload
    return await getUploadById(uploadId);
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

// Delete upload
export const deleteUpload = async (uploadId) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to delete content');
    }

    // Get the upload to check ownership and get the file URL
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    if (upload.userId !== user.uid) {
      throw new Error('You can only delete your own content');
    }

    // If the file is stored in Firebase Storage, delete it
    if (upload.fileUrl && upload.fileUrl.includes('firebasestorage.googleapis.com')) {
      try {
        const fileRef = ref(storage, upload.fileUrl);
        await deleteObject(fileRef);
      } catch (storageError) {
        console.error('Error deleting file from storage:', storageError);
        // Continue with deleting the document even if file deletion fails
      }
    }

    // Delete the upload document from Firestore
    await deleteDoc(doc(db, UPLOADS_COLLECTION, uploadId));

    return true;
  } catch (error) {
    console.error('Error deleting upload:', error);
    throw error;
  }
};
