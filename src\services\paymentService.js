import { 
  collection, 
  doc, 
  addDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase.js';
import { getCurrentUser } from './authService.js';

// Collections
const PAYMENTS_COLLECTION = 'payments';
const ENROLLMENTS_COLLECTION = 'enrollments';

/**
 * Payment statuses
 */
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded'
};

/**
 * Enrollment statuses
 */
export const ENROLLMENT_STATUS = {
  PENDING_PAYMENT: 'pending_payment',
  PENDING_APPROVAL: 'pending_approval',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  ACTIVE: 'active',
  SUSPENDED: 'suspended'
};

/**
 * Create a payment record for premium course enrollment
 */
export const createPayment = async (courseId, amount, currency = 'USD') => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to make a payment');
    }

    const paymentData = {
      userId: user.uid,
      userEmail: user.email,
      userName: user.displayName || user.email,
      courseId,
      amount: parseFloat(amount),
      currency: currency.toUpperCase(),
      status: PAYMENT_STATUS.PENDING,
      paymentMethod: 'stripe', // Default to Stripe
      transactionId: null,
      stripePaymentIntentId: null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
    };

    const paymentRef = await addDoc(collection(db, PAYMENTS_COLLECTION), paymentData);
    
    return {
      id: paymentRef.id,
      ...paymentData
    };
  } catch (error) {
    console.error('Error creating payment:', error);
    throw error;
  }
};

/**
 * Update payment status
 */
export const updatePaymentStatus = async (paymentId, status, transactionData = {}) => {
  try {
    const paymentRef = doc(db, PAYMENTS_COLLECTION, paymentId);
    
    const updateData = {
      status,
      updatedAt: serverTimestamp(),
      ...transactionData
    };

    if (status === PAYMENT_STATUS.COMPLETED) {
      updateData.completedAt = serverTimestamp();
    }

    await updateDoc(paymentRef, updateData);
    
    return true;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
};

/**
 * Get payment by ID
 */
export const getPaymentById = async (paymentId) => {
  try {
    const paymentDoc = await getDoc(doc(db, PAYMENTS_COLLECTION, paymentId));
    
    if (!paymentDoc.exists()) {
      return null;
    }

    return {
      id: paymentDoc.id,
      ...paymentDoc.data()
    };
  } catch (error) {
    console.error('Error getting payment:', error);
    return null;
  }
};

/**
 * Get user's payments
 */
export const getUserPayments = async () => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      return [];
    }

    const paymentsQuery = query(
      collection(db, PAYMENTS_COLLECTION),
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const paymentsSnapshot = await getDocs(paymentsQuery);
    
    const payments = [];
    paymentsSnapshot.forEach((doc) => {
      payments.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return payments;
  } catch (error) {
    console.error('Error getting user payments:', error);
    return [];
  }
};

/**
 * Create enrollment request for premium course
 */
export const createEnrollmentRequest = async (courseId, paymentId) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      throw new Error('You must be logged in to enroll');
    }

    const enrollmentData = {
      userId: user.uid,
      userEmail: user.email,
      userName: user.displayName || user.email,
      courseId,
      paymentId,
      status: ENROLLMENT_STATUS.PENDING_APPROVAL,
      requestedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      approvedBy: null,
      approvedAt: null,
      rejectedBy: null,
      rejectedAt: null,
      rejectionReason: null,
      notes: ''
    };

    const enrollmentRef = await addDoc(collection(db, ENROLLMENTS_COLLECTION), enrollmentData);
    
    return {
      id: enrollmentRef.id,
      ...enrollmentData
    };
  } catch (error) {
    console.error('Error creating enrollment request:', error);
    throw error;
  }
};

/**
 * Update enrollment status
 */
export const updateEnrollmentStatus = async (enrollmentId, status, adminData = {}) => {
  try {
    const enrollmentRef = doc(db, ENROLLMENTS_COLLECTION, enrollmentId);
    
    const updateData = {
      status,
      updatedAt: serverTimestamp(),
      ...adminData
    };

    if (status === ENROLLMENT_STATUS.APPROVED) {
      updateData.approvedAt = serverTimestamp();
    } else if (status === ENROLLMENT_STATUS.REJECTED) {
      updateData.rejectedAt = serverTimestamp();
    }

    await updateDoc(enrollmentRef, updateData);
    
    return true;
  } catch (error) {
    console.error('Error updating enrollment status:', error);
    throw error;
  }
};

/**
 * Get enrollment by course and user
 */
export const getEnrollmentByCourse = async (courseId) => {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      return null;
    }

    const enrollmentQuery = query(
      collection(db, ENROLLMENTS_COLLECTION),
      where('userId', '==', user.uid),
      where('courseId', '==', courseId)
    );

    const enrollmentSnapshot = await getDocs(enrollmentQuery);
    
    if (enrollmentSnapshot.empty) {
      return null;
    }

    const enrollmentDoc = enrollmentSnapshot.docs[0];
    return {
      id: enrollmentDoc.id,
      ...enrollmentDoc.data()
    };
  } catch (error) {
    console.error('Error getting enrollment:', error);
    return null;
  }
};

/**
 * Get all pending enrollments (for admin)
 */
export const getPendingEnrollments = async () => {
  try {
    const enrollmentsQuery = query(
      collection(db, ENROLLMENTS_COLLECTION),
      where('status', '==', ENROLLMENT_STATUS.PENDING_APPROVAL),
      orderBy('requestedAt', 'desc')
    );

    const enrollmentsSnapshot = await getDocs(enrollmentsQuery);
    
    const enrollments = [];
    enrollmentsSnapshot.forEach((doc) => {
      enrollments.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return enrollments;
  } catch (error) {
    console.error('Error getting pending enrollments:', error);
    return [];
  }
};

/**
 * Process Stripe payment
 */
export const processStripePayment = async (paymentId, paymentMethodId) => {
  try {
    // TODO: Integrate with actual Stripe API
    // This is a placeholder for real Stripe integration

    console.log('Processing Stripe payment:', { paymentId, paymentMethodId });

    throw new Error('Stripe integration not implemented yet');
    
    if (isSuccess) {
      const transactionId = 'txn_' + Math.random().toString(36).substr(2, 9);
      const stripePaymentIntentId = 'pi_' + Math.random().toString(36).substr(2, 9);
      
      await updatePaymentStatus(paymentId, PAYMENT_STATUS.COMPLETED, {
        transactionId,
        stripePaymentIntentId,
        paymentMethodId
      });
      
      return {
        success: true,
        transactionId,
        stripePaymentIntentId
      };
    } else {
      await updatePaymentStatus(paymentId, PAYMENT_STATUS.FAILED, {
        errorMessage: 'Payment failed - insufficient funds',
        paymentMethodId
      });
      
      throw new Error('Payment failed - insufficient funds');
    }
  } catch (error) {
    console.error('Error processing Stripe payment:', error);
    throw error;
  }
};

/**
 * Check if user has access to premium course
 */
export const hasAccessToPremiumCourse = async (courseId) => {
  try {
    const enrollment = await getEnrollmentByCourse(courseId);
    
    if (!enrollment) {
      return false;
    }

    return enrollment.status === ENROLLMENT_STATUS.APPROVED || 
           enrollment.status === ENROLLMENT_STATUS.ACTIVE;
  } catch (error) {
    console.error('Error checking premium course access:', error);
    return false;
  }
};
